<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserMapper">

    <!-- 用户结果映射 -->
    <resultMap id="UserResultMap" type="com.zte.uedm.digitalenergy.infrastructure.permission.po.UserPO">
        <id column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="user_code" property="userCode"/>
        <result column="organization" property="organization"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="com.zte.uedm.digitalenergy.infrastructure.permission.po.UserPO" 
            useGeneratedKeys="true" keyProperty="userId">
        INSERT INTO t_user (username, user_code, organization, create_time, create_by, update_time, update_by)
        VALUES (#{username}, #{userCode}, #{organization}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy})
    </insert>

    <!-- 更新用户 -->
    <update id="update" parameterType="com.zte.uedm.digitalenergy.infrastructure.permission.po.UserPO">
        UPDATE t_user 
        SET username = #{username},
            organization = #{organization},
            update_time = #{updateTime},
            update_by = #{updateBy}
        WHERE user_id = #{userId}
    </update>

    <!-- 根据ID查询用户 -->
    <select id="selectById" parameterType="long" resultMap="UserResultMap">
        SELECT * FROM t_user WHERE user_id = #{userId}
    </select>

    <!-- 根据用户工号查询用户 -->
    <select id="selectByUserCode" parameterType="string" resultMap="UserResultMap">
        SELECT * FROM t_user WHERE user_code = #{userCode}
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" parameterType="string" resultMap="UserResultMap">
        SELECT * FROM t_user WHERE username = #{username}
    </select>

    <!-- 查询所有用户 -->
    <select id="selectAll" resultMap="UserResultMap">
        SELECT * FROM t_user ORDER BY create_time DESC
    </select>

    <!-- 分页查询用户 -->
    <select id="selectByPage" resultMap="UserResultMap">
        SELECT * FROM t_user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="userCode != null and userCode != ''">
                AND user_code LIKE CONCAT('%', #{userCode}, '%')
            </if>
            <if test="organization != null and organization != ''">
                AND organization LIKE CONCAT('%', #{organization}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 统计用户总数 -->
    <select id="count" resultType="long">
        SELECT COUNT(*) FROM t_user
    </select>

    <!-- 根据ID删除用户 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM t_user WHERE user_id = #{userId}
    </delete>

    <!-- 批量删除用户 -->
    <delete id="deleteByIds" parameterType="list">
        DELETE FROM t_user WHERE user_id IN
        <foreach collection="list" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <!-- 检查用户工号是否存在 -->
    <select id="existsByUserCode" parameterType="string" resultType="int">
        SELECT COUNT(*) FROM t_user WHERE user_code = #{userCode}
    </select>

    <!-- 检查用户ID是否存在 -->
    <select id="existsById" parameterType="long" resultType="int">
        SELECT COUNT(*) FROM t_user WHERE user_id = #{userId}
    </select>

    <!-- 为用户分配角色 -->
    <insert id="assignRolesToUser">
        INSERT INTO t_user_role (user_id, role_id, create_time)
        VALUES
        <foreach collection="roleIds" item="roleId" separator=",">
            (#{userId}, #{roleId}, NOW())
        </foreach>
    </insert>

    <!-- 移除用户的角色 -->
    <delete id="removeRolesFromUser" parameterType="long">
        DELETE FROM t_user_role WHERE user_id = #{userId}
    </delete>

    <!-- 获取用户的直接角色 -->
    <select id="findDirectRoleIdsByUserId" parameterType="long" resultType="long">
        SELECT role_id FROM t_user_role WHERE user_id = #{userId}
    </select>

    <!-- 获取用户所属的用户组 -->
    <select id="findUserGroupIdsByUserId" parameterType="long" resultType="long">
        SELECT user_group_id FROM t_user_group_member WHERE user_id = #{userId}
    </select>

    <!-- 获取用户的所有角色（直接分配 + 用户组继承） -->
    <select id="findAllRoleIdsByUserId" parameterType="long" resultType="long">
        SELECT DISTINCT role_id FROM (
            SELECT role_id FROM t_user_role WHERE user_id = #{userId}
            UNION
            SELECT ugr.role_id FROM t_user_group_member ugm
            JOIN t_user_group_role ugr ON ugm.user_group_id = ugr.user_group_id
            WHERE ugm.user_id = #{userId}
        ) AS all_roles
    </select>

    <!-- 获取用户的所有菜单权限 -->
    <select id="findAllMenuIdsByUserId" parameterType="long" resultType="string">
        SELECT DISTINCT rm.menu_id FROM (
            SELECT role_id FROM t_user_role WHERE user_id = #{userId}
            UNION
            SELECT ugr.role_id FROM t_user_group_member ugm
            JOIN t_user_group_role ugr ON ugm.user_group_id = ugr.user_group_id
            WHERE ugm.user_id = #{userId}
        ) AS all_roles
        JOIN t_role_menu rm ON all_roles.role_id = rm.role_id
    </select>

    <!-- 根据角色ID查找用户 -->
    <select id="findByRoleId" parameterType="long" resultMap="UserResultMap">
        SELECT u.* FROM t_user u
        JOIN t_user_role ur ON u.user_id = ur.user_id
        WHERE ur.role_id = #{roleId}
    </select>

    <!-- 根据用户组ID查找用户 -->
    <select id="findByUserGroupId" parameterType="long" resultMap="UserResultMap">
        SELECT u.* FROM t_user u
        JOIN t_user_group_member ugm ON u.user_id = ugm.user_id
        WHERE ugm.user_group_id = #{userGroupId}
    </select>

    <!-- 从用户组中移除用户 -->
    <delete id="removeUserFromAllGroups" parameterType="long">
        DELETE FROM t_user_group_member WHERE user_id = #{userId}
    </delete>

</mapper>
