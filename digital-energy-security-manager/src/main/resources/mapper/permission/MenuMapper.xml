<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.digitalenergy.infrastructure.permission.mapper.MenuMapper">

    <!-- 菜单结果映射 -->
    <resultMap id="MenuResultMap" type="com.zte.uedm.digitalenergy.infrastructure.permission.po.MenuPO">
        <id column="menu_id" property="menuId"/>
        <result column="menu_name" property="menuName"/>
        <result column="parent_id" property="parentId"/>
        <result column="menu_path" property="menuPath"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="menu_level" property="menuLevel"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 插入菜单 -->
    <insert id="insert" parameterType="com.zte.uedm.digitalenergy.infrastructure.permission.po.MenuPO">
        INSERT INTO t_menu (menu_id, menu_name, parent_id, menu_path, sort_order, menu_level, create_time, update_time)
        VALUES (#{menuId}, #{menuName}, #{parentId}, #{menuPath}, #{sortOrder}, #{menuLevel}, #{createTime}, #{updateTime})
    </insert>

    <!-- 更新菜单 -->
    <update id="update" parameterType="com.zte.uedm.digitalenergy.infrastructure.permission.po.MenuPO">
        UPDATE t_menu 
        SET menu_name = #{menuName},
            parent_id = #{parentId},
            menu_path = #{menuPath},
            sort_order = #{sortOrder},
            menu_level = #{menuLevel},
            update_time = #{updateTime}
        WHERE menu_id = #{menuId}
    </update>

    <!-- 根据ID查询菜单 -->
    <select id="selectById" parameterType="string" resultMap="MenuResultMap">
        SELECT * FROM t_menu WHERE menu_id = #{menuId}
    </select>

    <!-- 根据菜单名称查询菜单 -->
    <select id="selectByMenuName" parameterType="string" resultMap="MenuResultMap">
        SELECT * FROM t_menu WHERE menu_name = #{menuName}
    </select>

    <!-- 查询所有菜单 -->
    <select id="selectAll" resultMap="MenuResultMap">
        SELECT * FROM t_menu ORDER BY menu_level, sort_order
    </select>

    <!-- 根据层级查询菜单 -->
    <select id="selectByMenuLevel" parameterType="int" resultMap="MenuResultMap">
        SELECT * FROM t_menu WHERE menu_level = #{menuLevel} ORDER BY sort_order
    </select>

    <!-- 根据父菜单ID查询子菜单 -->
    <select id="selectByParentId" parameterType="long" resultMap="MenuResultMap">
        SELECT * FROM t_menu WHERE parent_id = #{parentId} ORDER BY sort_order
    </select>

    <!-- 获取菜单树结构 -->
    <select id="selectMenuTree" resultMap="MenuResultMap">
        SELECT * FROM t_menu WHERE menu_level > 0 ORDER BY menu_level, sort_order
    </select>

    <!-- 获取指定菜单的子菜单树 -->
    <select id="selectSubMenuTree" parameterType="string" resultMap="MenuResultMap">
        SELECT * FROM t_menu 
        WHERE menu_id LIKE CONCAT(#{parentMenuId}, '%') 
        AND menu_id != #{parentMenuId}
        ORDER BY menu_level, sort_order
    </select>

    <!-- 根据ID删除菜单 -->
    <delete id="deleteById" parameterType="string">
        DELETE FROM t_menu WHERE menu_id = #{menuId}
    </delete>

    <!-- 批量删除菜单 -->
    <delete id="deleteByIds" parameterType="list">
        DELETE FROM t_menu WHERE menu_id IN
        <foreach collection="list" item="menuId" open="(" separator="," close=")">
            #{menuId}
        </foreach>
    </delete>

    <!-- 检查菜单ID是否存在 -->
    <select id="existsById" parameterType="string" resultType="boolean">
        SELECT COUNT(*) > 0 FROM t_menu WHERE menu_id = #{menuId}
    </select>

    <!-- 检查菜单名称是否存在 -->
    <select id="existsByMenuName" parameterType="string" resultType="boolean">
        SELECT COUNT(*) > 0 FROM t_menu WHERE menu_name = #{menuName}
    </select>

    <!-- 根据菜单ID列表查询菜单 -->
    <select id="selectByIds" parameterType="list" resultMap="MenuResultMap">
        SELECT * FROM t_menu WHERE menu_id IN
        <foreach collection="list" item="menuId" open="(" separator="," close=")">
            #{menuId}
        </foreach>
        ORDER BY menu_level, sort_order
    </select>

    <!-- 获取顶级菜单（第一层） -->
    <select id="selectTopLevelMenus" resultMap="MenuResultMap">
        SELECT * FROM t_menu WHERE menu_level = 1 ORDER BY sort_order
    </select>

    <!-- 根据路径查询菜单 -->
    <select id="selectByMenuPath" parameterType="string" resultMap="MenuResultMap">
        SELECT * FROM t_menu WHERE menu_path = #{menuPath}
    </select>

    <!-- 检查菜单是否有子菜单 -->
    <select id="hasChildren" parameterType="string" resultType="boolean">
        SELECT COUNT(*) > 0 FROM t_menu WHERE menu_id LIKE CONCAT(#{menuId}, '%') AND menu_id != #{menuId}
    </select>

    <!-- 获取菜单的所有子菜单ID（递归） -->
    <select id="selectAllChildMenuIds" parameterType="string" resultType="string">
        SELECT menu_id FROM t_menu 
        WHERE menu_id LIKE CONCAT(#{menuId}, '%') 
        AND menu_id != #{menuId}
    </select>

    <!-- 根据排序号排序查询菜单 -->
    <select id="selectByParentIdOrderBySortOrder" parameterType="long" resultMap="MenuResultMap">
        SELECT * FROM t_menu WHERE parent_id = #{parentId} ORDER BY sort_order
    </select>

</mapper>
