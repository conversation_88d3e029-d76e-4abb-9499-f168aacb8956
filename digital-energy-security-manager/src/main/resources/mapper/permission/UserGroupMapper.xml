<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserGroupMapper">

    <!-- 用户组结果映射 -->
    <resultMap id="UserGroupResultMap" type="com.zte.uedm.digitalenergy.infrastructure.permission.po.UserGroupPO">
        <id column="user_group_id" property="userGroupId"/>
        <result column="group_name" property="groupName"/>
        <result column="group_description" property="groupDescription"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <!-- 插入用户组 -->
    <insert id="insert" parameterType="com.zte.uedm.digitalenergy.infrastructure.permission.po.UserGroupPO" 
            useGeneratedKeys="true" keyProperty="userGroupId">
        INSERT INTO t_user_group (group_name, group_description, create_time, create_by, update_time, update_by)
        VALUES (#{groupName}, #{groupDescription}, #{createTime}, #{createBy}, #{updateTime}, #{updateBy})
    </insert>

    <!-- 更新用户组 -->
    <update id="update" parameterType="com.zte.uedm.digitalenergy.infrastructure.permission.po.UserGroupPO">
        UPDATE t_user_group 
        SET group_name = #{groupName},
            group_description = #{groupDescription},
            update_time = #{updateTime},
            update_by = #{updateBy}
        WHERE user_group_id = #{userGroupId}
    </update>

    <!-- 根据ID查询用户组 -->
    <select id="selectById" parameterType="long" resultMap="UserGroupResultMap">
        SELECT * FROM t_user_group WHERE user_group_id = #{userGroupId}
    </select>

    <!-- 根据用户组名称查询用户组 -->
    <select id="selectByGroupName" parameterType="string" resultMap="UserGroupResultMap">
        SELECT * FROM t_user_group WHERE group_name = #{groupName}
    </select>

    <!-- 查询所有用户组 -->
    <select id="selectAll" resultMap="UserGroupResultMap">
        SELECT * FROM t_user_group ORDER BY create_time DESC
    </select>

    <!-- 分页查询用户组 -->
    <select id="selectByPage" resultMap="UserGroupResultMap">
        SELECT * FROM t_user_group
        <where>
            <if test="groupName != null and groupName != ''">
                AND group_name LIKE CONCAT('%', #{groupName}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 统计用户组总数 -->
    <select id="count" resultType="long">
        SELECT COUNT(*) FROM t_user_group
    </select>

    <!-- 根据ID删除用户组 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM t_user_group WHERE user_group_id = #{userGroupId}
    </delete>

    <!-- 批量删除用户组 -->
    <delete id="deleteByIds" parameterType="list">
        DELETE FROM t_user_group WHERE user_group_id IN
        <foreach collection="list" item="userGroupId" open="(" separator="," close=")">
            #{userGroupId}
        </foreach>
    </delete>

    <!-- 检查用户组名称是否存在 -->
    <select id="existsByGroupName" parameterType="string" resultType="int">
        SELECT COUNT(*) FROM t_user_group WHERE group_name = #{groupName}
    </select>

    <!-- 为用户组分配角色 -->
    <insert id="assignRolesToUserGroup">
        INSERT INTO t_user_group_role (user_group_id, role_id, create_time)
        VALUES
        <foreach collection="roleIds" item="roleId" separator=",">
            (#{userGroupId}, #{roleId}, NOW())
        </foreach>
    </insert>

    <!-- 移除用户组的角色 -->
    <delete id="removeRolesFromUserGroup" parameterType="long">
        DELETE FROM t_user_group_role WHERE user_group_id = #{userGroupId}
    </delete>

    <!-- 获取用户组的角色 -->
    <select id="findRoleIdsByUserGroupId" parameterType="long" resultType="long">
        SELECT role_id FROM t_user_group_role WHERE user_group_id = #{userGroupId}
    </select>

    <!-- 为用户组添加成员 -->
    <insert id="addMembersToUserGroup">
        INSERT INTO t_user_group_member (user_group_id, user_id, create_time)
        VALUES
        <foreach collection="userIds" item="userId" separator=",">
            (#{userGroupId}, #{userId}, NOW())
        </foreach>
    </insert>

    <!-- 从用户组移除成员 -->
    <delete id="removeMembersFromUserGroup">
        DELETE FROM t_user_group_member 
        WHERE user_group_id = #{userGroupId} 
        AND user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <!-- 移除用户组的所有成员 -->
    <delete id="removeAllMembersFromUserGroup" parameterType="long">
        DELETE FROM t_user_group_member WHERE user_group_id = #{userGroupId}
    </delete>

    <!-- 获取用户组的成员 -->
    <select id="findMemberIdsByUserGroupId" parameterType="long" resultType="long">
        SELECT user_id FROM t_user_group_member WHERE user_group_id = #{userGroupId}
    </select>

    <!-- 获取用户组的所有菜单权限 -->
    <select id="findAllMenuIdsByUserGroupId" parameterType="long" resultType="string">
        SELECT DISTINCT rm.menu_id 
        FROM t_user_group_role ugr
        JOIN t_role_menu rm ON ugr.role_id = rm.role_id
        WHERE ugr.user_group_id = #{userGroupId}
    </select>

    <!-- 根据角色ID查找用户组 -->
    <select id="findByRoleId" parameterType="long" resultMap="UserGroupResultMap">
        SELECT ug.* FROM t_user_group ug
        JOIN t_user_group_role ugr ON ug.user_group_id = ugr.user_group_id
        WHERE ugr.role_id = #{roleId}
    </select>

    <!-- 根据用户ID查找用户组 -->
    <select id="findByUserId" parameterType="long" resultMap="UserGroupResultMap">
        SELECT ug.* FROM t_user_group ug
        JOIN t_user_group_member ugm ON ug.user_group_id = ugm.user_group_id
        WHERE ugm.user_id = #{userId}
    </select>

    <!-- 检查用户是否属于用户组 -->
    <select id="isUserInGroup" resultType="boolean">
        SELECT COUNT(*) > 0 FROM t_user_group_member 
        WHERE user_group_id = #{userGroupId} AND user_id = #{userId}
    </select>

    <!-- 统计用户组成员数量 -->
    <select id="countMembersByUserGroupId" parameterType="long" resultType="int">
        SELECT COUNT(*) FROM t_user_group_member WHERE user_group_id = #{userGroupId}
    </select>

</mapper>
