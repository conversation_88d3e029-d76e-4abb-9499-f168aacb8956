package com.zte.uedm.digitalenergy.infrastructure.permission.repository;

import com.github.pagehelper.PageHelper;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.Role;
import com.zte.uedm.digitalenergy.domain.permission.repository.RoleRepository;
import com.zte.uedm.digitalenergy.domain.permission.valueobj.RoleType;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.RoleMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.RoleMenuMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.RoleMenuPO;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.RolePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 角色仓储实现类
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Slf4j
@Repository
public class RoleRepositoryImpl implements RoleRepository {
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private RoleMenuMapper roleMenuMapper;
    
    @Override
    @Transactional
    public Role save(Role role) {
        if (role.getRoleId() == null) {
            // 新增角色
            RolePO rolePO = convertToRolePO(role);
            roleMapper.insert(rolePO);
            role.setRoleId(rolePO.getRoleId());
            log.info("Created new role: {}", role.getRoleName());
            
            // 保存角色菜单关联
            if (role.getMenuIds() != null && !role.getMenuIds().isEmpty()) {
                assignMenusToRole(role.getRoleId(), role.getMenuIds());
            }
        } else {
            // 更新角色
            RolePO rolePO = convertToRolePO(role);
            roleMapper.update(rolePO);
            log.info("Updated role: {}", role.getRoleName());
            
            // 更新角色菜单关联
            roleMenuMapper.deleteByRoleId(role.getRoleId());
            if (role.getMenuIds() != null && !role.getMenuIds().isEmpty()) {
                assignMenusToRole(role.getRoleId(), role.getMenuIds());
            }
        }
        return role;
    }
    
    @Override
    public Optional<Role> findById(Long roleId) {
        RolePO rolePO = roleMapper.selectById(roleId);
        if (rolePO == null) {
            return Optional.empty();
        }
        Role role = convertToRole(rolePO);
        // 加载菜单权限
        List<String> menuIds = roleMenuMapper.selectMenuIdsByRoleId(roleId);
        role.setMenuIds(menuIds);
        return Optional.of(role);
    }
    
    @Override
    public Optional<Role> findByRoleName(String roleName) {
        RolePO rolePO = roleMapper.selectByRoleName(roleName);
        if (rolePO == null) {
            return Optional.empty();
        }
        Role role = convertToRole(rolePO);
        // 加载菜单权限
        List<String> menuIds = roleMenuMapper.selectMenuIdsByRoleId(role.getRoleId());
        role.setMenuIds(menuIds);
        return Optional.of(role);
    }
    
    @Override
    public List<Role> findByRoleType(RoleType roleType) {
        List<RolePO> rolePOList = roleMapper.selectByRoleType(roleType.getCode());
        return rolePOList.stream()
                .map(this::convertToRole)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Role> findAll() {
        List<RolePO> rolePOList = roleMapper.selectAll();
        return rolePOList.stream()
                .map(this::convertToRole)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Role> findByPage(int pageNum, int pageSize, String roleName, RoleType roleType) {
        PageHelper.startPage(pageNum, pageSize);
        String roleTypeCode = roleType != null ? roleType.getCode() : null;
        List<RolePO> rolePOList = roleMapper.selectByPage(roleName, roleTypeCode);
        return rolePOList.stream()
                .map(this::convertToRole)
                .collect(Collectors.toList());
    }
    
    @Override
    public long count() {
        return roleMapper.count();
    }
    
    @Override
    public long countByRoleType(RoleType roleType) {
        return roleMapper.countByRoleType(roleType.getCode());
    }
    
    @Override
    @Transactional
    public void deleteById(Long roleId) {
        // 删除角色菜单关联
        roleMenuMapper.deleteByRoleId(roleId);
        // 删除角色
        roleMapper.deleteById(roleId);
        log.info("Deleted role with ID: {}", roleId);
    }
    
    @Override
    @Transactional
    public void deleteByIds(List<Long> roleIds) {
        for (Long roleId : roleIds) {
            roleMenuMapper.deleteByRoleId(roleId);
        }
        roleMapper.deleteByIds(roleIds);
        log.info("Deleted {} roles", roleIds.size());
    }
    
    @Override
    public boolean existsByRoleName(String roleName) {
        return roleMapper.existsByRoleName(roleName) > 0;
    }
    
    @Override
    public boolean isRoleUsedByUsers(Long roleId) {
        return roleMapper.countUsersByRoleId(roleId) > 0;
    }
    
    @Override
    public boolean isRoleUsedByUserGroups(Long roleId) {
        return roleMapper.countUserGroupsByRoleId(roleId) > 0;
    }
    
    @Override
    @Transactional
    public void assignMenusToRole(Long roleId, List<String> menuIds) {
        if (menuIds == null || menuIds.isEmpty()) {
            return;
        }
        
        List<RoleMenuPO> roleMenuList = menuIds.stream()
                .map(menuId -> {
                    RoleMenuPO roleMenuPO = new RoleMenuPO();
                    roleMenuPO.setRoleId(roleId);
                    roleMenuPO.setMenuId(menuId);
                    roleMenuPO.setCreateTime(LocalDateTime.now());
                    return roleMenuPO;
                })
                .collect(Collectors.toList());
        
        roleMenuMapper.batchInsert(roleMenuList);
        log.info("Assigned {} menus to role {}", menuIds.size(), roleId);
    }
    
    @Override
    @Transactional
    public void removeMenusFromRole(Long roleId) {
        roleMenuMapper.deleteByRoleId(roleId);
        log.info("Removed all menus from role {}", roleId);
    }
    
    @Override
    public List<String> findMenuIdsByRoleId(Long roleId) {
        return roleMenuMapper.selectMenuIdsByRoleId(roleId);
    }
    
    @Override
    public List<Long> findUserIdsByRoleId(Long roleId) {
        return roleMapper.selectUserIdsByRoleId(roleId);
    }
    
    @Override
    public List<Long> findUserGroupIdsByRoleId(Long roleId) {
        return roleMapper.selectUserGroupIdsByRoleId(roleId);
    }
    
    /**
     * 转换为领域对象
     */
    private Role convertToRole(RolePO rolePO) {
        Role role = new Role();
        role.setRoleId(rolePO.getRoleId());
        role.setRoleName(rolePO.getRoleName());
        role.setRoleDescription(rolePO.getRoleDescription());
        role.setRoleType(RoleType.fromCode(rolePO.getRoleType()));
        role.setCreateTime(rolePO.getCreateTime());
        role.setCreateBy(rolePO.getCreateBy());
        role.setUpdateTime(rolePO.getUpdateTime());
        role.setUpdateBy(rolePO.getUpdateBy());
        return role;
    }
    
    /**
     * 转换为持久化对象
     */
    private RolePO convertToRolePO(Role role) {
        RolePO rolePO = new RolePO();
        rolePO.setRoleId(role.getRoleId());
        rolePO.setRoleName(role.getRoleName());
        rolePO.setRoleDescription(role.getRoleDescription());
        rolePO.setRoleType(role.getRoleType().getCode());
        rolePO.setCreateTime(role.getCreateTime());
        rolePO.setCreateBy(role.getCreateBy());
        rolePO.setUpdateTime(role.getUpdateTime());
        rolePO.setUpdateBy(role.getUpdateBy());
        return rolePO;
    }
}
