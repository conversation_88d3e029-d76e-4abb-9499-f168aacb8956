package com.zte.uedm.digitalenergy.domain.permission.repository;

import com.zte.uedm.digitalenergy.domain.permission.aggregate.User;

import java.util.List;
import java.util.Optional;

/**
 * 用户仓储接口
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
public interface UserRepository {
    
    /**
     * 保存用户
     */
    User save(User user);
    
    /**
     * 根据ID查找用户
     */
    Optional<User> findById(Long userId);
    
    /**
     * 根据用户工号查找用户
     */
    Optional<User> findByUserCode(String userCode);
    
    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 查找所有用户
     */
    List<User> findAll();
    
    /**
     * 分页查询用户
     */
    List<User> findByPage(int pageNum, int pageSize, String username, String userCode, String organization);
    
    /**
     * 统计用户总数
     */
    long count();
    
    /**
     * 删除用户
     */
    void deleteById(Long userId);
    
    /**
     * 批量删除用户
     */
    void deleteByIds(List<Long> userIds);
    
    /**
     * 检查用户工号是否存在
     */
    boolean existsByUserCode(String userCode);
    
    /**
     * 为用户分配角色
     */
    void assignRolesToUser(Long userId, List<Long> roleIds);
    
    /**
     * 移除用户的角色
     */
    void removeRolesFromUser(Long userId);
    
    /**
     * 获取用户的直接角色
     */
    List<Long> findDirectRoleIdsByUserId(Long userId);
    
    /**
     * 获取用户所属的用户组
     */
    List<Long> findUserGroupIdsByUserId(Long userId);
    
    /**
     * 获取用户的所有角色（直接分配 + 用户组继承）
     */
    List<Long> findAllRoleIdsByUserId(Long userId);
    
    /**
     * 获取用户的所有菜单权限
     */
    List<String> findAllMenuIdsByUserId(Long userId);
    
    /**
     * 根据角色ID查找用户
     */
    List<User> findByRoleId(Long roleId);
    
    /**
     * 根据用户组ID查找用户
     */
    List<User> findByUserGroupId(Long userGroupId);
    
    /**
     * 从用户组中移除用户
     */
    void removeUserFromAllGroups(Long userId);
}
